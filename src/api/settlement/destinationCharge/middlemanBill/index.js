import request from "@/utils/request";

/**
 * 分页查询居间商账单信息
 * 支持多种筛选条件的组合查询，包括站点编号、站点名称、省市、账单周期、负责人、居间渠道商等
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {Array<number>} [data.orgNoList] - 组织编号列表
 * @param {number} [data.operatorId] - 操作员ID
 * @param {string} [data.operatorName] - 操作员名称
 * @param {string} [data.stationCode] - 站点编号（模糊查询）
 * @param {string} [data.stationName] - 站点名称（模糊查询）
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.city] - 市编码
 * @param {string} [data.billPeriodStart] - 账单周期开始日期（格式：YYYY-MM-DD）
 * @param {string} [data.billPeriodEnd] - 账单周期结束日期（格式：YYYY-MM-DD）
 * @param {string} [data.responsiblePerson] - 负责人（模糊查询）
 * @param {string} [data.intermediaryChannel] - 居间渠道商（字典：intermediary_channel）
 * @returns {Promise<Object>} 返回分页查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>} returns.data - 居间商账单信息列表
 * @returns {number} returns.data[].intermediaryId - 主键ID
 * @returns {string} returns.data[].stationCode - 站点编号
 * @returns {string} returns.data[].stationName - 站点名称
 * @returns {string} returns.data[].province - 省份编码
 * @returns {string} returns.data[].provinceName - 省份名称
 * @returns {string} returns.data[].city - 市编码
 * @returns {string} returns.data[].cityName - 市名称
 * @returns {string} returns.data[].responsiblePerson - 负责人
 * @returns {string} returns.data[].billPeriodStart - 账单周期开始日期
 * @returns {string} returns.data[].billPeriodEnd - 账单周期结束日期
 * @returns {string} returns.data[].billPeriod - 账单周期显示文本
 * @returns {string} returns.data[].intermediaryChannel - 居间渠道商编码（字典：intermediary_channel）
 * @returns {string} returns.data[].intermediaryChannelName - 居间渠道商名称
 * @returns {number} returns.data[].profitSharingRatio - 分润比例（百分比）
 * @returns {number} returns.data[].settlementAmount - 结算金额（元）
 * @returns {string} returns.data[].specialNotes - 特殊情况备注（最多500个字符）
 * @returns {number} returns.data[].tenantId - 租户号
 * @returns {number} returns.data[].orgNo - 组织编号
 * @returns {string} returns.data[].orgNoName - 组织名称
 * @returns {number} returns.data[].creator - 创建人ID
 * @returns {string} returns.data[].creatorName - 创建人名称
 * @returns {string} returns.data[].createTime - 创建时间
 * @returns {string} returns.data[].updateTime - 更新时间
 * @returns {number} returns.data[].operatorId - 操作员ID
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 查询第一页数据
 * const result = await middlemanBillApi.queryList({
 *   pageNum: 1,
 *   pageSize: 10,
 *   stationCode: 'ST001'
 * });
 */
export function queryList(data) {
  return request({
    url: "/intermediary/bill/queryList",
    method: "post",
    data,
  });
}

/**
 * 分页查询负责人列表
 * 用于获取负责人下拉选择器的数据源
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {Array<number>} [data.orgNoList] - 组织编号列表
 * @param {number} [data.operatorId] - 操作员ID
 * @param {string} [data.operatorName] - 操作员名称
 * @param {string} [data.stationCode] - 站点编号
 * @param {string} [data.stationName] - 站点名称
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.city] - 市编码
 * @param {string} [data.billPeriodStart] - 账单周期开始日期
 * @param {string} [data.billPeriodEnd] - 账单周期结束日期
 * @param {string} [data.responsiblePerson] - 负责人
 * @param {string} [data.intermediaryChannel] - 居间渠道商
 * @returns {Promise<Object>} 返回负责人列表查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<string>} returns.data - 负责人名称列表
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 获取负责人列表
 * const result = await middlemanBillApi.queryResponsiblePersonList({
 *   pageNum: 1,
 *   pageSize: 50
 * });
 */
export function queryResponsiblePersonList(data) {
  return request({
    url: "/intermediary/bill/queryResponsiblePersonList",
    method: "post",
    data,
  });
}

/**
 * 删除居间商账单信息
 * @param {Object} params - 删除参数
 * @param {number} params.intermediaryId - 要删除的居间商账单信息ID（必填）
 * @returns {Promise<Object>} 返回删除结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {null} returns.data - 删除结果数据（通常为null）
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 删除居间商账单信息
 * const result = await middlemanBillApi.remove({ intermediaryId: 1 });
 */
export function remove(params) {
  return request({
    url: "/intermediary/bill/remove",
    method: "get",
    params,
  });
}

/**
 * 导出居间商账单信息Excel文件
 * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
 * @param {number} [data.pageNum] - 页码
 * @param {number} [data.pageSize] - 每页条数
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {Array<number>} [data.orgNoList] - 组织编号列表
 * @param {number} [data.operatorId] - 操作员ID
 * @param {string} [data.operatorName] - 操作员名称
 * @param {string} [data.stationCode] - 站点编号
 * @param {string} [data.stationName] - 站点名称
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.city] - 市编码
 * @param {string} [data.billPeriodStart] - 账单周期开始日期
 * @param {string} [data.billPeriodEnd] - 账单周期结束日期
 * @param {string} [data.responsiblePerson] - 负责人
 * @param {string} [data.intermediaryChannel] - 居间渠道商
 * @returns {Promise<Object>} 返回导出结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 导出文件下载链接或文件内容
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 导出所有居间商账单信息
 * const result = await middlemanBillApi.exportData({
 *   stationCode: 'ST001'
 * });
 */
export function exportData(data) {
  return request({
    url: "/intermediary/bill/export",
    method: "post",
    data,
  });
}

/**
 * 导入居间商账单信息Excel文件
 * @param {FormData} data - 包含文件的FormData对象
 * @param {File} data.file - 要导入的Excel文件（必填）
 * @returns {Promise<Object>} 返回导入结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
 * @returns {string} returns.data - 导入结果详情
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 导入Excel文件
 * const formData = new FormData();
 * formData.append('file', file);
 * const result = await middlemanBillApi.importData(formData);
 */
export function importData(data) {
  return request({
    url: "/intermediary/bill/import",
    method: "get",
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 新增/编辑居间商账单信息
 * @param {Object} data - 居间商账单信息数据
 * @param {number} [data.intermediaryId] - 主键ID（编辑时必填）
 * @param {string} data.stationCode - 站点编号（必填）
 * @param {string} data.stationName - 站点名称（必填）
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.provinceName] - 省份名称
 * @param {string} [data.city] - 市编码
 * @param {string} [data.cityName] - 市名称
 * @param {string} data.responsiblePerson - 负责人（必填）
 * @param {string} data.billPeriodStart - 账单周期开始日期（必填）
 * @param {string} data.billPeriodEnd - 账单周期结束日期（必填）
 * @param {string} [data.billPeriod] - 账单周期显示文本
 * @param {string} data.intermediaryChannel - 居间渠道商编码（必填，字典：intermediary_channel）
 * @param {string} [data.intermediaryChannelName] - 居间渠道商名称
 * @param {number} [data.profitSharingRatio] - 分润比例（百分比）
 * @param {number} [data.settlementAmount] - 结算金额（元）
 * @param {string} [data.specialNotes] - 特殊情况备注（最多500个字符）
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {string} [data.orgNoName] - 组织名称
 * @param {number} [data.creator] - 创建人ID
 * @param {string} [data.creatorName] - 创建人名称
 * @param {string} [data.createTime] - 创建时间
 * @param {string} [data.updateTime] - 更新时间
 * @param {number} [data.operatorId] - 操作员ID
 * @returns {Promise<Object>} 返回新增/编辑结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {null} returns.data - 操作结果数据（通常为null）
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 新增居间商账单信息
 * const result = await middlemanBillApi.update({
 *   stationCode: 'ST001',
 *   stationName: '测试站点',
 *   responsiblePerson: '张三',
 *   billPeriodStart: '2024-01-01',
 *   billPeriodEnd: '2024-01-31',
 *   intermediaryChannel: 'channel_001'
 * });
 */
export function update(data) {
  return request({
    url: "/intermediary/bill/saveInfo",
    method: "post",
    data,
  });
}

// 导出所有方法
export default {
  queryList,
  queryResponsiblePersonList,
  remove,
  exportData,
  importData,
  update,
};
