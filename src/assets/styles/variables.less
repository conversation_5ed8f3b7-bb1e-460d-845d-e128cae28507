// base color
@blue: #324157;
@light-blue: #3a71a8;
@red: #c03639;
@pink: #e65d6e;
@green: #30b08f;
@tiffany: #4ab7bd;
@yellow: #fec171;
@panGreen: #30b08f;

// sidebar
// @menuText:#bfcbd9;
// @menuActiveText:#409EFF;
// @subMenuActiveText:#f4f4f5; // https://github.com/ElemeFE/element/issues/12951
@menuText: white;
@menuActiveText: white;
@subMenuActiveText: white; // https://github.com/ElemeFE/element/issues/12951

// @menuBg:#304156;
// @menuHover:#263445;

// @subMenuBg:#1f2d3d;
// @subMenuHover:#001528;

@menuBg: #02726a;
@menuHover: #065852;

@subMenuBg: #4571ff;
@subMenuHover: #065852;

@sideBarWidth: 220px;

@menuActive: #065852;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: @menuText;
  menuActiveText: @menuActiveText;
  subMenuActiveText: @subMenuActiveText;
  menuBg: @menuBg;
  menuHover: @menuHover;
  menuActive: @menuActive;
  subMenuBg: @subMenuBg;
  subMenuHover: @subMenuHover;
  sideBarWidth: @sideBarWidth;
}
